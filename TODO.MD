TODO.MD

- [ ] Update navbar to follow the scroll of menu
- [ ] Update cart to reflect price change for options
- [ ] Update code for checkout
- [ ] Able to retrive cart for modify

## TODO

### [ ] add visual indicator for discount

While the discounted field is stored in the database and can be set through the admin interface, there doesn't appear to be any special display logic in the frontend to show users that an item is discounted. The discounted price is collected but not prominently displayed or highlighted in the user interface.

If you want to show the discounted price to users, you would need to modify the relevant components to:

- Display both the original and discounted prices
- Add visual indicators (like strikethrough, different colors, or badges)
- Potentially show the discount percentage or amount saved

discounted 字段的用途

1. **数据结构**：

    - 在 MongoDB 模型中定义为 Number类型，默认值为 0
    - 在 GraphQL schema 中定义为 Float类型，可为空

2. **使用场景**：

    - 用于表示商品的折扣价格
    - 主要应用在 Variation（商品变体）模型中
    - 在订单处理、购物车和结账流程中使用
3. **业务逻辑**：

    - 当 discounted 有值时，表示该商品有折扣
    - 在价格计算时，会优先使用 discounted 价格而不是原价
    - 在多个地方（如 orderFsmActions.js, dialog.js, 订单解析器等）都有使用，确保在创建订单项时正确应用折扣价

#### 取值范围

1. **类型**：数值型（Number/Float）
2. **有效范围**：
    - 最小值为 0（表示无折扣）
    - 最大值通常小于或等于原价（price 字段）
    - 可以为空（null 或 undefined），此时表示不应用折扣
3. **默认值**：0（表示默认没有折扣）

#### 使用建议

1. 在设置 discounted 值时，应该确保：
    - discounted <= price
    - discounted >= 0
2. 在前端展示时，应该检查 discounted 是否有值：
    - 如果有值且大于 0，则显示折扣价
    - 否则显示原价

这个字段是电商系统中常见的价格字段，用于实现商品的折扣功能。

## 安全审计发现

### 1. 认证漏洞

- **URL路径中的令牌**：认证令牌通过URL路径（`/m/` 或 `/a/`）暴露在 [`src/utils/tokenUtils.ts:1-16`](src/utils/tokenUtils.ts:1-16)
  - 风险：高 - 令牌可能泄露到浏览器历史记录、服务器日志和referrer中
  - 影响：会话劫持和账户泄露
- **无令牌有效期**：令牌没有过期时间 ([`src/apollo-client.ts:10-26`](src/apollo-client.ts:10-26))
  - 风险：中 - 令牌泄露后攻击者可长期访问系统

### 2. 授权缺陷

- **缺少所有权验证**：操作直接接受ID参数而未验证资源所有权
  - 示例：
    - `deleteCustomerAddress` 在 [`Doc/customer.graphql:86`](Doc/customer.graphql:86)
    - `cancelOrder` 在 [`Doc/order.graphql:142`](Doc/order.graphql:142)
  - 风险：高 - 允许未授权数据修改（IDOR攻击）
- **过度权限查询**：GraphQL类型暴露敏感字段
  - 客户PII泄露： [`Doc/customer.graphql:45-61`](Doc/customer.graphql:45-61)
  - 支付详情泄露： [`Doc/order.graphql:77-80`](Doc/order.graphql:77-80)

### 3. 输入验证问题

- **缺少输入过滤**：
  - 地址服务未验证电话号码、邮箱或坐标 ([`src/services/addressService.ts:1-15`](src/services/addressService.ts:1-15))
  - 文本字段（specialInstructions, deliveryInstructions）缺少长度限制
- **GraphQL输入风险**：
  - `placeOrder` 操作缺少验证 ([`Doc/order.graphql:127-145`](Doc/order.graphql:127-145))
  - 未转义字符串可能导致XSS攻击

### 4. 数据保护漏洞

- **敏感数据暴露**：
  - 支付方法和状态以明文存储 ([`Doc/order.graphql:77-80`](Doc/order.graphql:77-80))
  - 客户订单历史过度获取 ([`Doc/customer.graphql:55-57`](Doc/customer.graphql:55-57))
- **错误处理不安全**：
  - 原始错误输出到控制台 ([`src/utils/errorHandling.ts:36,86`](src/utils/errorHandling.ts:36,86))
  - 错误消息可能泄露系统信息

### 5. API安全弱点

- **缺少速率限制**：未防护暴力破解攻击
  - 认证接口
  - 订单提交接口 ([`Doc/order.graphql:127-145`](Doc/order.graphql:127-145))
- **不安全默认值**：硬编码country="IE" ([`Doc/customer.graphql:81`](Doc/customer.graphql:81))

### 安全改进建议

```mermaid
graph TD
    A[关键修复] --> A1[将令牌移至HTTP-only cookies]
    A --> A2[添加资源所有权验证]
    
    B[高优先级] --> B1[添加输入验证中间件]
    B --> B2[加密敏感支付数据]
    B --> B3[优化GraphQL查询范围]
    
    C[中优先级] --> C1[实施速率限制]
    C --> C2[添加错误消息脱敏]
    C --> C3[审计默认配置]
```
