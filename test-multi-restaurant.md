# 多餐厅功能测试指南

## 测试环境
- 开发服务器: http://localhost:3008/
- 测试餐厅: wl (Wicklow), sl (Swords)
- 测试token: test-token

## 测试用例

### 1. 品牌首页测试
- **URL**: http://localhost:3008/
- **预期结果**: 
  - 显示Eskimo Pizza品牌信息
  - 显示两个餐厅位置（Wicklow和Swords）
  - 每个餐厅显示地址、电话、营业时间等信息
  - 点击"Order Now"按钮显示提示信息

### 2. Wicklow餐厅菜单测试
- **URL**: http://localhost:3008/wl/test-token
- **预期结果**:
  - 加载Wicklow餐厅数据
  - 显示餐厅名称: "Eskimo Pizza - Wicklow"
  - 显示菜单分类和食品项目
  - 购物车功能正常

### 3. Swords餐厅菜单测试
- **URL**: http://localhost:3008/sl/test-token
- **预期结果**:
  - 加载Swords餐厅数据
  - 显示餐厅名称: "Eskimo Pizza - Swords"
  - 显示菜单分类和食品项目
  - 购物车功能正常

### 4. 地址选择页面测试
- **Wicklow URL**: http://localhost:3008/wl/a/test-token
- **Swords URL**: http://localhost:3008/sl/a/test-token
- **预期结果**:
  - 正确显示地址选择页面
  - "Add New Address"按钮导航到正确的餐厅路径
  - "Back to Cart"按钮返回到对应餐厅的菜单

### 5. 添加地址页面测试
- **Wicklow URL**: http://localhost:3008/wl/a/test-token/add
- **Swords URL**: http://localhost:3008/sl/a/test-token/add
- **预期结果**:
  - 正确显示地址表单
  - "Cancel"按钮返回到对应餐厅的地址选择页面

### 6. 路由跳转测试
- 从菜单页面点击购物车的"Choose Pickup"或"Proceed to Address"
- **预期结果**: 正确跳转到包含餐厅ID的地址页面

### 7. 错误处理测试
- **无效餐厅ID**: http://localhost:3008/invalid/test-token
- **预期结果**: 显示错误页面，提示餐厅未找到

## 验证要点

### ✅ 数据加载
- [ ] 不同餐厅加载不同的数据文件
- [ ] 餐厅名称和地址正确显示
- [ ] 菜单数据正确加载

### ✅ 路由功能
- [ ] 所有路由格式正确: /:restaurantId/:token
- [ ] 地址相关路由包含餐厅ID
- [ ] 导航链接正确生成

### ✅ 购物车功能
- [ ] 购物车在不同餐厅间独立
- [ ] 地址跳转包含正确的餐厅ID
- [ ] 订单提交流程正常

### ✅ 静态部署兼容性
- [ ] _redirects文件配置正确
- [ ] SPA路由在刷新后仍然工作
- [ ] 所有静态资源正确加载

## 已知限制

1. **Token验证**: 当前使用测试token，实际环境需要有效的WhatsApp token
2. **GraphQL查询**: 某些功能需要后端API支持
3. **品牌首页**: "Order Now"按钮目前只显示提示，实际应该集成WhatsApp链接

## 成功标准

- ✅ 所有路由正确工作
- ✅ 不同餐厅显示不同数据
- ✅ 购物车和地址功能正常
- ✅ 无JavaScript错误
- ✅ 响应式设计在移动端正常工作
