import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { submitOrder, OrderResponse } from '../services/orderService';
import { WHATSAPP_ERROR_TYPES } from '../utils/errorHandling';
import '../styles/Cart.css';
import '../styles/Error.css';
import { useCart } from '../context/CartContext';
import restaurantData from '../data/restaurant.json';
import { formatPrice, CURRENCY_SYMBOL } from '../config';

interface CartItemType {
  menuItem: {
    MenuItemId: string;
    Name: string;
    Price: number;
  };
  quantity: number;
  selectedOptions: {
    optionSetId: string;
    optionIds: string[];
  }[];
  variation?: {
    _id: string;
    title: string;
    price: number;
  };
  cartItemId: string;
  specialInstructions?: string;
}

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const { restaurantId, token } = useParams<{ restaurantId: string; token: string }>();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [checkoutError, setCheckoutError] = useState<string | null>(null);
  const { cart, removeFromCart, updateQuantity, clearCart, option_map } = useCart();

  const [orderDetails, setOrderDetails] = useState({
    instructions: '',
    tipping: 0,
  });

  // 添加点击外部区域处理
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const cartElement = document.querySelector('.cart-container');
      if (isExpanded && cartElement && !cartElement.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isExpanded]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const did = urlParams.get('d');
    const rid = urlParams.get('r');

    if (did && rid) {
      setOrderDetails(prev => ({
        ...prev,
        customerId: did,
        restaurantId: rid,
      }));
    }
  }, []);

  const totalItems = cart.reduce((sum: number, item) => sum + item.quantity, 0);

  const calculateItemPrice = (item: CartItemType) => {
    // Always use variation price if available
    const basePrice = item.variation ? item.variation.price : item.menuItem.Price;
    let addonsPrice = 0;

    // Calculate price from selected options
    item.selectedOptions.forEach(selectedOption => {
      selectedOption.optionIds.forEach(optionId => {
        if (option_map[optionId]) {
          addonsPrice += option_map[optionId].price;
        }
      });
    });
    return (basePrice + addonsPrice) * item.quantity;
  };

  const calculateTotalPrice = () => {
    return cart.reduce((total, item) => total + calculateItemPrice(item), 0);
  };

  const handleQuantityChange = (cartItemId: string, newQuantity: number) => {
    updateQuantity(cartItemId, newQuantity);
  };

  const handleRemoveItem = (cartItemId: string) => {
    removeFromCart(cartItemId);
  };

  const handleCancelOrder = () => {
    clearCart();
    setIsExpanded(false);
  };

  const handleCheckout = async () => {
    if (cart.length === 0) {
      setCheckoutError('Your cart is empty');
      return;
    }

    setIsCheckingOut(true);
    setCheckoutError(null);

    try {
      const orderInput = cart.map(item => ({
        food: String(item.menuItem.MenuItemId), // Convert to string to ensure compatibility
        quantity: item.quantity,
        variation: item.variation?._id || '',
        addons: item.selectedOptions.map(opt => ({
          _id: opt.optionSetId,
          options: opt.optionIds,
        })),
        specialInstructions: item.specialInstructions || '',
      }));

      // 从 restaurant.json 获取餐厅ID
      const restaurantId = restaurantData.restaurant._id;

      // 将订单信息存储在 sessionStorage 中
      sessionStorage.setItem(
        'pendingOrder',
        JSON.stringify({
          orderInput,
          restaurantId,
          instructions: orderDetails.instructions,
          tipping: orderDetails.tipping,
        })
      );

      // 跳转到带餐厅ID和token的地址选择页面
      if (restaurantId && token) {
        navigate(`/${restaurantId}/a/${token}`);
      } else {
        console.error('Missing restaurantId or token for navigation');
        navigate('/error', {
          state: {
            error: 'Missing restaurant ID or token',
            title: 'Navigation Error',
          },
        });
      }
    } catch (error) {
      console.error('Error preparing order:', error);
      let errorMessage = error instanceof Error ? error.message : 'Failed to prepare order. Please try again.';

      // 检查是否是WhatsApp授权错误
      const isWhatsAppAuthError = Object.values(WHATSAPP_ERROR_TYPES).some(errorType =>
        errorMessage.includes(errorType.message)
      );

      // 如果是WhatsApp授权错误，直接使用错误消息
      // 否则，使用通用错误消息
      if (!isWhatsAppAuthError) {
        errorMessage = 'Failed to prepare order. Please try again.';
      }

      setCheckoutError(errorMessage);
      setIsExpanded(true); // 确保错误消息可见
    } finally {
      setIsCheckingOut(false);
    }
  };

  const toggleCart = () => {
    setIsExpanded(!isExpanded);
  };

  const getItemDetails = (item: any) => {
    let details = '';

    // Add variation if present
    if (item.variation) {
      details += item.variation.title;
    }

    // Add selected options
    item.selectedOptions.forEach((selectedOption: any) => {
      selectedOption.optionIds.forEach((optionId: string) => {
        if (option_map[optionId]) {
          details += details ? ', ' + option_map[optionId].title : option_map[optionId].title;
        }
      });
    });

    return details;
  };

  const handleOrderDetailsChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setOrderDetails(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  return (
    <div className={`cart-container ${isExpanded ? 'expanded' : ''}`}>
      <div className="cart-header" onClick={toggleCart}>
        <div className="cart-icon">
          <div className="cart-badge">{totalItems}</div>
          <span>Your Order</span>
        </div>
        <div className="cart-total">{formatPrice(calculateTotalPrice())}</div>
      </div>

      {isExpanded && (
        <div className="cart-dropdown">
          {checkoutError && <div className="checkout-error">{checkoutError}</div>}

          {cart.length === 0 ? (
            <div className="empty-cart">
              <p>Your cart is empty</p>
            </div>
          ) : (
            <>
              <div className="cart-items">
                {cart.map(item => (
                  <div key={item.cartItemId} className="cart-item">
                    <div className="cart-item-details">
                      <h3>{item.menuItem.Name}</h3>
                      <p className="item-options">{getItemDetails(item)}</p>
                    </div>
                    <div className="cart-item-price">{formatPrice(calculateItemPrice(item))}</div>
                    <div className="cart-item-actions">
                      <button
                        className="quantity-btn"
                        onClick={e => {
                          e.stopPropagation();
                          handleQuantityChange(item.cartItemId, item.quantity - 1);
                        }}
                        disabled={item.quantity <= 1}
                      >
                        -
                      </button>
                      <span className="item-quantity">{item.quantity}</span>
                      <button
                        className="quantity-btn"
                        onClick={e => {
                          e.stopPropagation();
                          handleQuantityChange(item.cartItemId, item.quantity + 1);
                        }}
                      >
                        +
                      </button>
                      <button
                        className="remove-btn"
                        onClick={e => {
                          e.stopPropagation();
                          handleRemoveItem(item.cartItemId);
                        }}
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="cart-summary">
                <div className="summary-row">
                  <span>Subtotal</span>
                  <span>{formatPrice(calculateTotalPrice())}</span>
                </div>

                <div className="summary-row total">
                  <span>Total</span>
                  <span>{formatPrice(calculateTotalPrice())}</span>
                </div>
              </div>

              <div className="order-details">
                <div className="order-details-row">
                  <div className="tipping-section">
                    <label className="detail-label">Tip Amount</label>
                    <div className="tipping-input-container">
                      <span className="currency-symbol">{CURRENCY_SYMBOL}</span>
                      <input
                        type="number"
                        name="tipping"
                        placeholder="0.00"
                        value={orderDetails.tipping}
                        onChange={handleOrderDetailsChange}
                        min="0"
                        step="0.01"
                        className="tipping-input"
                      />
                    </div>
                  </div>
                </div>

                <div className="order-details-row">
                  <label className="detail-label">Special Instructions</label>
                  <textarea
                    name="instructions"
                    placeholder="Add any special requests here..."
                    value={orderDetails.instructions}
                    onChange={handleOrderDetailsChange}
                    className="instructions-input"
                    rows={2}
                  />
                </div>
              </div>

              <button className="checkout-btn" onClick={handleCheckout} disabled={isCheckingOut || cart.length === 0}>
                {isCheckingOut ? 'Processing...' : `Checkout ${formatPrice(calculateTotalPrice())}`}
              </button>

              <button className="cancel-btn" onClick={handleCancelOrder} disabled={isCheckingOut}>
                Cancel Order
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Cart;
