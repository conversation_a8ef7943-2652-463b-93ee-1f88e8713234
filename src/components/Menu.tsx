import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import '../styles/Menu.css';
import MenuItemModal from './MenuItemModal';
import { useCart } from '../context/CartContext';
import { useRestaurant } from '../context/RestaurantContext';
import { useQuery } from '@apollo/client';
import { GET_SESSION_DATA } from '../graphql/queries';
import { WHATSAPP_ERROR_TYPES } from '../utils/errorHandling';
import { message } from 'antd';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { formatPrice } from '../config';
import { IOption, IAddon, IVariation, IFood, ICategory, IRestaurant, SelectedOption } from '../types/restaurant';

const Menu: React.FC = () => {
  const navigate = useNavigate();
  const { restaurantId, token } = useParams<{ restaurantId: string; token: string }>();
  const { restaurantData, loadRestaurant, isLoading: restaurantLoading, error: restaurantError } = useRestaurant();
  const [selectedItem, setSelectedItem] = useState<IFood | null>(null);
  const [activeCategory, setActiveCategory] = useState<number>(0);
  const { addToCart, initializeCart } = useCart();
  const [isCartInitialized, setIsCartInitialized] = useState(false);
  const { handleError, clearError } = useErrorHandler();

  // Refs for elements
  const categoryNavRef = useRef<HTMLDivElement>(null);
  const categoryRefs = useRef<(HTMLLIElement | null)[]>([]);
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const menuContainerRef = useRef<HTMLDivElement>(null);

  // Helper Dictionaries
  const [option_map, setOptionMap] = useState<Record<string, IOption>>({});
  const [addon_map, setAddonMap] = useState<Record<string, IAddon>>({});

  // Constants for layout calculations
  const HEADER_HEIGHT = 80; // Adjust based on your header height
  const CATEGORY_NAV_HEIGHT = 60; // Adjust based on your category nav height
  const SCROLL_OFFSET = HEADER_HEIGHT + CATEGORY_NAV_HEIGHT + 20; // Extra padding

  // Load restaurant data when restaurantId changes
  useEffect(() => {
    if (restaurantId) {
      loadRestaurant(restaurantId);
    }
  }, [restaurantId, loadRestaurant]);

  // Validate required parameters
  useEffect(() => {
    if (!restaurantId || !token) {
      handleError('Missing restaurant ID or token');
      navigate('/error', {
        state: {
          error: 'Missing restaurant ID or token',
          title: 'Invalid URL',
        },
      });
    }
  }, [restaurantId, token, handleError, navigate]);

  // Handle restaurant loading errors
  useEffect(() => {
    if (restaurantError) {
      handleError(restaurantError);
      navigate('/error', {
        state: {
          error: restaurantError,
          title: 'Restaurant Loading Error',
        },
      });
    }
  }, [restaurantError, handleError, navigate]);

  // Get session data
  const { loading: sessionLoading } = useQuery(GET_SESSION_DATA, {
    variables: { token },
    skip: !token || isCartInitialized,
    fetchPolicy: 'network-only',
    onCompleted: data => {
      setIsCartInitialized(true);
      if (data && data.getSessionByToken) {
        console.log('Session data received:', data.getSessionByToken);
        const success = initializeCart(data.getSessionByToken);
        if (success) {
          message.success('Your cart has been pre-filled with items');
        }
      } else {
        console.log('No session data found, starting with empty cart');
      }
      clearError();
    },
    onError: error => {
      handleError(error.message);
      navigate('/error', {
        state: {
          error: error.message,
          title: 'Authentication Error',
        },
      });
    },
  });

  // Initialize refs arrays and setup layout observer
  useEffect(() => {
    if (!restaurantData) return;

    categoryRefs.current = new Array(restaurantData.categories.length).fill(null);
    sectionRefs.current = new Array(restaurantData.categories.length).fill(null);

    // Setup ResizeObserver to monitor layout changes
    const categoryNav = categoryNavRef.current;
    if (categoryNav) {
      console.log('Setting up ResizeObserver');

      const resizeObserver = new ResizeObserver(() => {
        // Use requestAnimationFrame to ensure layout is stable
        requestAnimationFrame(() => {
          console.log('Category nav layout changed:', {
            scrollWidth: categoryNav.scrollWidth,
            clientWidth: categoryNav.clientWidth,
            canScroll: categoryNav.scrollWidth > categoryNav.clientWidth,
          });
        });
      });

      resizeObserver.observe(categoryNav);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [restaurantData]);

  // Function to scroll category nav horizontally
  const scrollCategoryNavTo = React.useCallback((index: number) => {
    console.log('📱 scrollCategoryNavTo called with index:', index);

    const categoryNav = categoryNavRef.current; // This is the .category-nav div element
    const categoryItem = categoryRefs.current[index];

    console.log('📱 Category nav ref (div):', categoryNav);
    console.log('📱 Category item ref:', categoryItem);
    console.log('📱 Category refs array length:', categoryRefs.current.length);

    if (!categoryNav) {
      console.log('❌ Missing category nav ref for scroll');
      return;
    }

    if (!categoryItem) {
      console.log('❌ Missing category item ref for index:', index);
      console.log(
        '📱 Available category refs:',
        categoryRefs.current.map((ref, i) => ({ index: i, hasRef: !!ref }))
      );
      return;
    }

    // 使用 requestAnimationFrame 确保所有布局计算在渲染完成后执行
    requestAnimationFrame(() => {
      console.log('📱 Executing category nav scroll in requestAnimationFrame');

      // 确保元素已经渲染并且样式已应用
      const navRect = categoryNav.getBoundingClientRect();
      const itemRect = categoryItem.getBoundingClientRect();
      const currentScrollLeft = categoryNav.scrollLeft;

      // Calculate the position to scroll to (item at left side of nav with some padding)
      const targetScrollLeft = currentScrollLeft + itemRect.left - navRect.left - 16; // 16px padding

      console.log('📱 Category nav scroll calculation:', {
        currentScrollLeft,
        targetScrollLeft: Math.max(0, targetScrollLeft),
        navScrollWidth: categoryNav.scrollWidth,
        navClientWidth: categoryNav.clientWidth,
        canScroll: categoryNav.scrollWidth > categoryNav.clientWidth,
        navRect,
        itemRect,
      });

      const finalScrollLeft = Math.max(0, targetScrollLeft);

      try {
        categoryNav.scrollTo({
          left: finalScrollLeft,
          behavior: 'smooth',
        });
        console.log('✅ Category nav scrollTo executed successfully');
      } catch (error) {
        console.log('❌ Category nav scrollTo failed, using scrollLeft:', error);
        categoryNav.scrollLeft = finalScrollLeft;
      }
    });
  }, []);

  // Handle category click
  const handleCategoryClick = React.useCallback(
    (index: number) => {
      const section = sectionRefs.current[index];
      const menuContainer = menuContainerRef.current;

      console.log('Category clicked:', index);
      console.log('Section ref:', section);
      console.log('Menu container ref:', menuContainer);

      if (!section || !menuContainer || !restaurantData) {
        console.log('Missing refs or data for category click');
        return;
      }

      // Update active category immediately for better UX
      console.log('Updating active category to:', index);
      setActiveCategory(index);
      scrollCategoryNavTo(index);

      // 使用 requestAnimationFrame 确保所有布局计算在渲染完成后执行
      requestAnimationFrame(() => {
        // 确保元素已经渲染并且样式已应用
        const sectionTop = section.offsetTop;
        let scrollTop = sectionTop - SCROLL_OFFSET;

        console.log('Scroll calculation (in rAF):', {
          sectionTop,
          SCROLL_OFFSET,
          calculatedScrollTop: scrollTop,
        });

        // Special handling for the last category
        if (index === restaurantData.categories.length - 1) {
          // For the last category, scroll to show it at the top but ensure we don't scroll past the end
          const maxScrollTop = menuContainer.scrollHeight - menuContainer.clientHeight;
          scrollTop = Math.min(scrollTop, maxScrollTop);
          console.log('Last category adjustment:', { maxScrollTop, finalScrollTop: scrollTop });
        }

        const finalScrollTop = Math.max(0, scrollTop);
        console.log('Scrolling menu to (in rAF):', finalScrollTop);

        try {
          menuContainer.scrollTo({
            top: finalScrollTop,
            behavior: 'smooth',
          });

          // Verify the scroll happened
          setTimeout(() => {
            console.log('After scroll - menu scrollTop:', menuContainer.scrollTop);
          }, 100);
        } catch (error) {
          console.log('Menu scrollTo failed, using scrollTop:', error);
          menuContainer.scrollTop = finalScrollTop;
        }
      });
    },
    [restaurantData, SCROLL_OFFSET, scrollCategoryNavTo]
  );

  // Handle menu scroll to update active category
  const handleMenuScroll = React.useCallback(() => {
    console.log('🔄 handleMenuScroll called');

    const menuContainer = menuContainerRef.current;
    if (!menuContainer) {
      console.log('❌ No menu container ref');
      return;
    }

    if (!restaurantData) {
      console.log('❌ No restaurant data');
      return;
    }

    const scrollTop = menuContainer.scrollTop;
    const containerHeight = menuContainer.clientHeight;
    const scrollBottom = scrollTop + containerHeight;
    const totalHeight = menuContainer.scrollHeight;

    console.log('📊 Menu scroll metrics:', {
      scrollTop,
      containerHeight,
      scrollBottom,
      totalHeight,
      categoriesCount: restaurantData.categories.length,
      sectionsRefsLength: sectionRefs.current.length,
    });

    let newActiveIndex = 0;

    // Check if we're at the bottom - if so, activate the last category
    if (scrollBottom >= totalHeight - 10) {
      // 10px threshold for bottom detection
      newActiveIndex = restaurantData.categories.length - 1;
      console.log('📍 At bottom, activating last category:', newActiveIndex);
    } else {
      // Find the category that should be active based on scroll position
      // We want the category whose section is at or just past the top of the visible area
      let bestIndex = 0;
      let bestDistance = Infinity;

      console.log('🔍 Checking sections for best match:');
      sectionRefs.current.forEach((section, index) => {
        if (!section) {
          console.log(`  Section ${index}: null ref`);
          return;
        }

        const sectionTop = section.offsetTop - SCROLL_OFFSET;
        const distance = Math.abs(scrollTop - sectionTop);

        console.log(`  Section ${index} (${restaurantData.categories[index]?.title}):`, {
          offsetTop: section.offsetTop,
          sectionTop,
          distance,
          isInRange: sectionTop <= scrollTop + 50,
          isBetter: distance < bestDistance,
        });

        // If this section is at or above the current scroll position and closer than previous best
        if (sectionTop <= scrollTop + 50 && distance < bestDistance) {
          // 50px tolerance
          bestDistance = distance;
          bestIndex = index;
          console.log(`  ✅ New best: index ${index}, distance ${distance}`);
        }
      });

      newActiveIndex = bestIndex;
      console.log('🎯 Final best category index:', newActiveIndex);
    }

    // Use functional update to get the latest activeCategory value
    setActiveCategory(currentActiveCategory => {
      console.log('🔄 setActiveCategory callback called:', {
        currentActiveCategory,
        newActiveIndex,
        willUpdate: newActiveIndex !== currentActiveCategory,
      });

      if (newActiveIndex !== currentActiveCategory) {
        console.log('✅ Updating active category from', currentActiveCategory, 'to', newActiveIndex);
        console.log('📱 Calling scrollCategoryNavTo with index:', newActiveIndex);
        scrollCategoryNavTo(newActiveIndex);
        return newActiveIndex;
      } else {
        console.log('⏭️ No category change needed');
      }
      return currentActiveCategory;
    });
  }, [restaurantData, SCROLL_OFFSET, scrollCategoryNavTo]);

  // Debounced scroll handler for better performance
  const debouncedHandleMenuScroll = useRef<NodeJS.Timeout | null>(null);

  // Ref to store the latest handleMenuScroll function
  const handleMenuScrollRef = useRef(handleMenuScroll);

  // Update the ref whenever handleMenuScroll changes
  useEffect(() => {
    handleMenuScrollRef.current = handleMenuScroll;
  }, [handleMenuScroll]);

  // Set up scroll listener when all conditions are met
  useEffect(() => {
    console.log('🔧 Checking conditions for scroll listener setup');

    const menuContainer = menuContainerRef.current;

    console.log('📋 Conditions check:', {
      hasRestaurantData: !!restaurantData,
      hasMenuContainer: !!menuContainer,
      categoriesLength: restaurantData?.categories?.length || 0,
    });

    // 检查所有必要条件
    if (!restaurantData || !menuContainer) {
      console.log('❌ Not all conditions met for scroll listener setup');
      return;
    }

    console.log('✅ All conditions met, setting up scroll listener');

    // 等待字体加载完成，然后设置滚动监听器
    const setupScrollListener = () => {
      console.log('🎯 Setting up scroll listener on menu container');
      console.log('📦 Menu container element:', menuContainer);

      // Use a direct function to avoid dependency issues
      const scrollHandler = () => {
        console.log('🚀 Scroll event detected on menu container');
        console.log('⏰ Current scroll position:', menuContainer.scrollTop);

        if (debouncedHandleMenuScroll.current) {
          clearTimeout(debouncedHandleMenuScroll.current);
          console.log('🔄 Cleared previous debounce timeout');
        }

        debouncedHandleMenuScroll.current = setTimeout(() => {
          console.log('⚡ Executing handleMenuScroll after debounce');
          handleMenuScrollRef.current();
        }, 50);
      };

      menuContainer.addEventListener('scroll', scrollHandler);
      console.log('✅ Scroll event listener added to menu container');

      return () => {
        console.log('🧹 Cleaning up scroll listener');
        menuContainer.removeEventListener('scroll', scrollHandler);
        if (debouncedHandleMenuScroll.current) {
          clearTimeout(debouncedHandleMenuScroll.current);
        }
      };
    };

    let cleanup: (() => void) | undefined;

    // 使用 document.fonts.ready 确保字体加载完成
    if (document.fonts && document.fonts.ready) {
      document.fonts.ready.then(() => {
        console.log('🔤 Fonts loaded, setting up scroll listener');
        // 使用 requestAnimationFrame 确保布局稳定
        requestAnimationFrame(() => {
          cleanup = setupScrollListener();
        });
      });
    } else {
      // 如果不支持 fonts API，使用 requestAnimationFrame 延迟
      console.log('⚠️ Fonts API not supported, using requestAnimationFrame delay');
      requestAnimationFrame(() => {
        cleanup = setupScrollListener();
      });
    }

    return () => {
      console.log('🔚 Scroll listener useEffect cleanup');
      if (cleanup) {
        cleanup();
      }
    };
  }, [restaurantData, menuContainerRef.current]); // 依赖 restaurantData 和 menuContainer

  // 初始自动定位逻辑 - 页面加载后自动滚动到第一个分类
  useEffect(() => {
    if (!restaurantData || !categoryNavRef.current || !menuContainerRef.current) return;

    // 等待字体和布局稳定后执行初始定位
    const performInitialPositioning = () => {
      requestAnimationFrame(() => {
        console.log('Performing initial positioning');
        // 可以在这里添加初始滚动逻辑，比如滚动到第一个category
        // 目前保持默认位置，但确保category navigation可见
        const categoryNav = categoryNavRef.current;
        if (categoryNav) {
          console.log('Initial category nav state:', {
            scrollWidth: categoryNav.scrollWidth,
            clientWidth: categoryNav.clientWidth,
            canScroll: categoryNav.scrollWidth > categoryNav.clientWidth,
          });
        }
      });
    };

    if (document.fonts && document.fonts.ready) {
      document.fonts.ready.then(performInitialPositioning);
    } else {
      // 延迟执行以确保CSS和布局稳定
      setTimeout(performInitialPositioning, 100);
    }
  }, [restaurantData]);

  // Build option and addon maps
  useEffect(() => {
    if (!restaurantData) return;

    const newOptionMap: Record<string, IOption> = {};
    restaurantData.options.forEach(option => {
      newOptionMap[option._id] = option;
    });
    setOptionMap(newOptionMap);

    const newAddonMap: Record<string, IAddon> = {};
    restaurantData.addons.forEach(addon => {
      newAddonMap[addon._id] = addon;
    });
    setAddonMap(newAddonMap);
  }, [restaurantData]);

  const handleAddClick = (item: IFood) => {
    if (!item.variations || item.variations.length === 0) {
      addToCart({
        menuItem: {
          MenuItemId: item._id,
          Name: item.title,
          Price: 0,
        },
        selectedOptions: [],
        quantity: 1,
      });
    } else {
      setSelectedItem(item);
    }
  };

  const handleModalClose = () => {
    setSelectedItem(null);
  };

  const findOption = (optionId: string, variation: IVariation): IOption | undefined => {
    for (const addonId of variation.addons) {
      const addon = addon_map[addonId];
      if (!addon) continue;
      for (const optId of addon.options) {
        if (optionId == optId) {
          const option = option_map[optId];
          if (option) return option;
        }
      }
    }
    return undefined;
  };

  const handleModalConfirm = (item: IFood, variation: IVariation, quantity: number, selectedOptions: SelectedOption[]) => {
    addToCart({
      menuItem: {
        MenuItemId: item._id,
        Name: item.title,
        Price: variation.price,
      },
      variation: {
        _id: variation._id,
        title: variation.title,
        price: variation.price,
      },
      quantity,
      selectedOptions,
    });
    setSelectedItem(null);
  };

  if (restaurantLoading || !restaurantData || sessionLoading) {
    return <div>Loading menu{sessionLoading ? ' and cart data' : ''}...</div>;
  }

  return (
    <div className="menu">
      {/* Fixed Restaurant Header */}
      <div className="restaurant-header">
        <h1>{restaurantData.name}</h1>
        {restaurantData.logo && (
          <img src={restaurantData.logo} alt={`${restaurantData.name} logo`} className="restaurant-logo" />
        )}
      </div>

      {/* Fixed Category Navigation */}
      <div className="category-nav" ref={categoryNavRef}>
        <ul>
          {restaurantData.categories.map((category, index) => (
            <li
              key={category._id}
              className={`category-item ${index === activeCategory ? 'active' : ''}`}
              ref={el => (categoryRefs.current[index] = el)}
              onClick={() => handleCategoryClick(index)}
            >
              {category.title}
            </li>
          ))}
        </ul>
      </div>

      {/* Scrollable Menu Content */}
      <div className="menu-container" ref={menuContainerRef}>
        <div className="menu-grid">
          {restaurantData.categories.map((category, index) => (
            <div key={category._id} className="menu-section menu-category" ref={el => (sectionRefs.current[index] = el)}>
              <h2>{category.title}</h2>
              <div className="menu-items">
                {category.foods.map(item => (
                  <div key={item._id} className="menu-item">
                    {item.image && <img src={item.image} alt={item.title} />}
                    <div className="menu-item-content">
                      <h3 className="menu-item-title">{item.title}</h3>
                      <p className="menu-item-description">{item.description}</p>
                      <div className="price-container">
                        <p className="price">{formatPrice(item.variations[0] ? item.variations[0].price : 0)}</p>
                        <button className="add-to-cart-button" onClick={() => handleAddClick(item)}>
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedItem && (
        <MenuItemModal
          item={selectedItem}
          onClose={handleModalClose}
          onConfirm={handleModalConfirm}
          option_map={option_map}
          addon_map={addon_map}
          findOption={findOption}
        />
      )}
    </div>
  );
};

export default Menu;
