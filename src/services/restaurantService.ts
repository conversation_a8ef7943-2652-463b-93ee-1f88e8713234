import { IRestaurant } from '../types/restaurant';

export interface RestaurantDataResponse {
  restaurant: IRestaurant;
}

/**
 * Load restaurant data by restaurant ID
 * @param restaurantId - The restaurant identifier (e.g., 'wl', 'sl')
 * @returns Promise<RestaurantDataResponse> - The restaurant data response
 */
export const loadRestaurantData = async (restaurantId: string): Promise<RestaurantDataResponse> => {
  try {
    const response = await fetch(`/data/restaurants/${restaurantId}.json`);

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Restaurant '${restaurantId}' not found`);
      }
      throw new Error(`Failed to load restaurant data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Validate that the response has the expected structure
    if (!data || !data.restaurant) {
      throw new Error(`Invalid restaurant data format for '${restaurantId}'`);
    }

    return data;
  } catch (error) {
    console.error(`Error loading restaurant data for '${restaurantId}':`, error);
    throw error;
  }
};

/**
 * Get list of available restaurant shortcodes
 * This is a simple implementation that could be expanded to read from a config file
 * @returns string[] - Array of available restaurant shortcodes
 */
export const getAvailableRestaurants = (): string[] => {
  // For now, return hardcoded list. In the future, this could read from a config file
  return ['wl', 'sl'];
};

/**
 * Validate if a restaurant shortcode exists
 * @param shortcode - The restaurant shortcode (e.g., 'wl', 'sl')
 * @returns boolean - Whether the restaurant exists
 */
export const validateRestaurantExists = (shortcode: string): boolean => {
  const availableRestaurants = getAvailableRestaurants();
  return availableRestaurants.includes(shortcode);
};
