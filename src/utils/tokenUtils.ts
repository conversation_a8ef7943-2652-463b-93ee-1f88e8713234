export function extractTokenFromPath(): string {
  const pathParts = window.location.pathname.split('/');

  // New URL format: /:restaurantId/:token or /:restaurantId/a/:token
  // pathParts = ["", "wl", "token"] or ["", "wl", "a", "token"]

  if (pathParts.length < 3) {
    throw new Error('Invalid URL format: missing token');
  }

  // Check if it's an address route (/:restaurantId/a/:token)
  if (pathParts.length >= 4 && pathParts[2] === 'a') {
    return pathParts[3]; // token is at index 3
  }

  // Check if it's a menu route (/:restaurantId/:token)
  if (pathParts.length >= 3) {
    return pathParts[2]; // token is at index 2
  }

  throw new Error('Invalid URL format: missing token');
}
