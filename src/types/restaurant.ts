export interface IOption {
  _id: string;
  title: string;
  price: number;
  description: string | null;
}

export interface IAddon {
  _id: string;
  title: string;
  options: string[];
  description: string | null;
  quantityMinimum: number;
  quantityMaximum: number;
}

export interface IVariation {
  _id: string;
  title: string;
  price: number;
  discounted: number | null;
  addons: string[];
}

export interface IFood {
  _id: string;
  title: string;
  description: string;
  variations: IVariation[];
  image: string | null;
  isActive?: boolean;
}

export interface ICategory {
  _id: string;
  title: string;
  foods: IFood[];
}

export interface IRestaurant {
  _id: string;
  name: string;
  image: string | null;
  logo?: string | null;
  address?: string;
  deliveryTime?: number;
  minimumOrder?: number;
  tax?: number;
  shopType?: string;
  cuisines?: string[];
  tags?: string[];
  phone?: string | null;
  restaurantBrand?: string;
  restaurantBrandId?: string;
  restaurantUrl?: string | null;
  deliveryCostType?: string;
  deliveryCostRate?: number | null;
  deliveryCostMin?: number;
  openingTimes?: Array<{
    day: string;
    times: Array<{
      startTime: string[];
      endTime: string[];
    }>;
  }>;
  categories: ICategory[];
  options: IOption[];
  addons: IAddon[];
}

export interface SelectedOption {
  optionSetId: string;
  optionIds: string[];
}
