import React, { useEffect, useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { useNavigate, useParams } from 'react-router-dom';
import { Button, List, Modal, message, Card, Space, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CheckOutlined, ShopOutlined } from '@ant-design/icons';
import '../styles/Address.css';
import { GET_CUSTOMER_ADDRESSES, DELETE_CUSTOMER_ADDRESS } from '../graphql/queries';
import { submitOrderWithAddress } from '../services/orderService';
import { useErrorHandler } from '../hooks/useErrorHandler';

const AddressSelection: React.FC = () => {
  const navigate = useNavigate();
  const { restaurantId, token } = useParams<{ restaurantId: string; token: string }>();
  const { handleError, clearError } = useErrorHandler();
  const [addresses, setAddresses] = useState<any[]>([]);
  const [pendingOrder, setPendingOrder] = useState<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Query addresses
  const { loading, data, refetch } = useQuery(GET_CUSTOMER_ADDRESSES, {
    variables: {
      limit: 20,
    },
    fetchPolicy: 'network-only', // Always fetch from network
    onError: error => {
      handleError(error.message);
      navigate('/error', {
        state: {
          error: error.message,
          title: 'Error Loading Addresses',
        },
      });
    },
  });

  // Delete address mutation
  const [deleteAddress] = useMutation(DELETE_CUSTOMER_ADDRESS, {
    onError: error => {
      handleError(error.message);
      navigate('/error', {
        state: {
          error: error.message,
          title: 'Error Deleting Address',
        },
      });
    },
    onCompleted: () => {
      message.success('Address deleted successfully');
      clearError();
      refetch();
    },
  });

  // Get pending order from sessionStorage
  useEffect(() => {
    const orderData = sessionStorage.getItem('pendingOrder');
    if (orderData) {
      try {
        setPendingOrder(JSON.parse(orderData));
      } catch (e) {
        console.error('Error parsing pending order:', e);
        handleError('Invalid order data');
        navigate('/error', {
          state: {
            error: 'Invalid order data. Please try again.',
            title: 'Order Error',
          },
        });
      }
    }
  }, [handleError]);

  // Update addresses list when data changes
  useEffect(() => {
    if (data?.customerAddresses) {
      setAddresses(data.customerAddresses);
    }
  }, [data]);

  // Add new address
  const handleAddAddress = () => {
    if (restaurantId && token) {
      navigate(`/${restaurantId}/a/${token}/add`);
    } else {
      console.error('Missing restaurantId or token for navigation');
      navigate('/error', {
        state: {
          error: 'Missing restaurant ID or token',
          title: 'Navigation Error',
        },
      });
    }
  };

  // Edit address
  const handleEditAddress = (addressId: string) => {
    if (restaurantId && token) {
      navigate(`/${restaurantId}/a/${token}/edit/${addressId}`);
    } else {
      console.error('Missing restaurantId or token for navigation');
      navigate('/error', {
        state: {
          error: 'Missing restaurant ID or token',
          title: 'Navigation Error',
        },
      });
    }
  };

  // Delete address
  const handleDeleteAddress = async (addressId: string) => {
    Modal.confirm({
      title: 'Delete Address',
      content: 'Are you sure you want to delete this address?',
      onOk: async () => {
        try {
          await deleteAddress({
            variables: {
              addressId,
            },
          });
          // Success is handled in onCompleted callback
        } catch (error) {
          // Error is already handled in the mutation's onError callback
          console.error('Delete address error:', error);
        }
      },
    });
  };

  // Select address and submit order
  const handleSelectAddress = async (address: any) => {
    if (!pendingOrder) {
      handleError('No order information found. Please try again.');
      navigate('/error', {
        state: {
          error: 'No order information found. Please try again.',
          title: 'Order Error',
        },
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await submitOrderWithAddress({
        ...pendingOrder,
        deliveryAddressId: address.addressId,
        isPickedUp: false, // Explicitly set isPickedUp to false for delivery orders
      });

      // Clear order from sessionStorage
      sessionStorage.removeItem('pendingOrder');

      // Show success message
      //message.success('Order placed successfully!');
      // Clear any error
      clearError();

      navigate('/success');
    } catch (error) {
      console.error('Error submitting order:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to place order. Please try again.';
      handleError(errorMessage);
      navigate('/error', {
        state: {
          error: errorMessage,
          title: 'Order Submission Error',
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle pickup option
  const handlePickup = async () => {
    if (!pendingOrder) {
      handleError('No order information found. Please try again.');
      navigate('/error', {
        state: {
          error: 'No order information found. Please try again.',
          title: 'Order Error',
        },
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Call submit order API with pickup flag
      await submitOrderWithAddress({
        ...pendingOrder,
        isPickedUp: true,
      });

      // Clear order from sessionStorage
      sessionStorage.removeItem('pendingOrder');

      // Show success message
      message.success('Pickup order placed successfully!');
      // Clear any error
      clearError();

      // Navigate to success page
      navigate('/success');
    } catch (error) {
      console.error('Error submitting pickup order:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to place pickup order. Please try again.';
      handleError(errorMessage);
      navigate('/error', {
        state: {
          error: errorMessage,
          title: 'Pickup Order Error',
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Return to cart
  const handleBackToCart = () => {
    clearError();
    if (restaurantId && token) {
      navigate(`/${restaurantId}/${token}`);
    } else {
      navigate(-1); // Fallback to previous page
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="address-container">
      <div className="address-header">
        <h1>Select Delivery Method</h1>
      </div>

      {!pendingOrder && (
        <div className="no-order-warning">
          <p>No order information found. Please return to cart and try again.</p>
          <Button type="primary" onClick={handleBackToCart}>
            Back to Cart
          </Button>
        </div>
      )}

      {pendingOrder && (
        <Card className="pickup-option">
          <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
            <div className="pickup-header">
              <h2>
                <ShopOutlined /> Self Pickup
              </h2>
              <p>Pick up your order from the restaurant</p>
            </div>
            <Button
              type="primary"
              icon={<ShopOutlined />}
              onClick={handlePickup}
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              Choose Pickup
            </Button>
          </Space>
        </Card>
      )}

      <Divider>
        <span className="divider-text">Or Choose Delivery Address</span>
      </Divider>

      <List
        className="address-list"
        itemLayout="horizontal"
        dataSource={addresses}
        locale={{
          emptyText: (
            <div className="empty-address-container">
              <p>No addresses found. Add your first address!</p>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAddress} style={{ marginTop: '10px' }}>
                Add New Address
              </Button>
            </div>
          ),
        }}
        renderItem={address => (
          <List.Item className="address-item">
            <div style={{ width: '100%' }}>
              <List.Item.Meta
                title={address.recipientName}
                description={
                  <div className="address-item-content">
                    <p>{address.formattedAddress}</p>
                    <p>Phone: {address.phone}</p>
                    {address.tag && <p>Tag: {address.tag}</p>}
                  </div>
                }
              />
              <div
                style={{
                  marginTop: '8px',
                  display: 'flex',
                  gap: '8px',
                  justifyContent: 'center',
                  maxWidth: '90%',
                  margin: '0 auto',
                }}
              >
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={() => handleSelectAddress(address)}
                  disabled={isSubmitting || !pendingOrder}
                  loading={isSubmitting}
                >
                  Deliver Here
                </Button>
                <Button icon={<EditOutlined />} onClick={() => handleEditAddress(address.addressId)}>
                  Edit
                </Button>
                <Button icon={<DeleteOutlined />} danger onClick={() => handleDeleteAddress(address.addressId)}>
                  Delete
                </Button>
              </div>
            </div>
          </List.Item>
        )}
      />

      {addresses.length > 0 && (
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAddress}>
            Add New Address
          </Button>
        </div>
      )}

      <div className="address-footer">
        <Button onClick={handleBackToCart}>Back to Cart</Button>
      </div>
    </div>
  );
};

export default AddressSelection;
