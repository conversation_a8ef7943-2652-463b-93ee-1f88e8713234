import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Form, Input, Button, message } from 'antd';
import { useMutation, useLazyQuery, useApolloClient } from '@apollo/client';
import { GET_ADDRESS_FROM_POSTCODE, ADD_CUSTOMER_ADDRESS } from '../graphql/queries';
import { handleWhatsAppGraphQLError } from '../utils/errorHandling';
import '../styles/Address.css';

const AddressForm: React.FC = () => {
  const { restaurantId, addressId, token } = useParams<{ restaurantId: string; addressId?: string; token: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const client = useApolloClient();
  const [form] = Form.useForm();
  const [addressDetails, setAddressDetails] = useState<any>(null);

  // 确定返回的路径
  const getReturnPath = (): string => {
    if (restaurantId && token) {
      return `/${restaurantId}/a/${token}`;
    }
    console.error('Missing restaurantId or token for navigation');
    return '/error';
  };

  // Use GraphQL query for postcode lookup
  const [getAddressFromPostcode, { loading: postcodeLoading }] = useLazyQuery(GET_ADDRESS_FROM_POSTCODE, {
    onCompleted: data => {
      const address = data.getAddressFromPostcode;
      form.setFieldsValue({
        streetAddress: address.streetAddress,
        city: address.city,
        state: address.state,
        country: address.country || 'IE',
      });
      setAddressDetails(address);
    },
    onError: error => {
      if (!handleWhatsAppGraphQLError(error)) {
        message.error('Failed to find address from postcode');
        console.error('Postcode lookup error:', error);
      }
    },
  });

  const handlePostcodeSearch = (postcode: string) => {
    getAddressFromPostcode({
      variables: {
        postcode,
        country: 'IE',
      },
    });
  };

  const handlePostcodeLookup = () => {
    const postcode = form.getFieldValue('postcode');
    if (postcode) {
      handlePostcodeSearch(postcode);
    }
  };

  // Use GraphQL mutation for adding address
  const [addAddress, { loading: addAddressLoading }] = useMutation(ADD_CUSTOMER_ADDRESS, {
    onCompleted: () => {
      message.success('Address saved successfully');

      // 重置地址列表缓存，强制重新查询
      client.cache.evict({ fieldName: 'customerAddresses' });
      client.cache.gc();

      // 返回到正确的页面
      navigate(getReturnPath());
    },
    onError: error => {
      if (!handleWhatsAppGraphQLError(error)) {
        console.error('Error saving address:', error);
        // Check if it's a network error that might be a 401
        if (error.networkError && 'statusCode' in error.networkError && error.networkError.statusCode === 401) {
          // Already handled by handleWhatsAppGraphQLError
        } else {
          message.error('Failed to save address');
        }
      }
    },
  });

  const onFinish = async (values: any) => {
    if (!addressDetails) {
      message.error('Please lookup postcode first');
      return;
    }

    const addressInput = {
      placeId: addressDetails.placeId,
      streetAddress: values.streetAddress,
      city: values.city,
      state: values.state,
      postcode: values.postcode,
      country: values.country || 'IE',
      coordinates: {
        latitude: addressDetails.coordinates.latitude,
        longitude: addressDetails.coordinates.longitude,
      },
      recipientName: values.recipientName,
      phone: values.phone,
      tag: values.tag,
    };

    addAddress({
      variables: {
        address: addressInput,
      },
    });
  };

  return (
    <div className="address-container">
      <div className="address-form">
        <h1 className="address-form-title">{addressId ? 'Edit Address' : 'Add New Address'}</h1>

        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item label="Eircode/Postcode" name="postcode" rules={[{ required: true, message: 'Please input postcode' }]}>
            <Input.Search
              placeholder="Enter postcode"
              loading={postcodeLoading}
              enterButton="Find Address"
              onSearch={handlePostcodeLookup}
            />
          </Form.Item>

          <Form.Item label="Street Address" name="streetAddress" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item label="City" name="city" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item label="State/County" name="state">
            <Input />
          </Form.Item>

          <Form.Item label="Country" name="country">
            <Input defaultValue="IE" />
          </Form.Item>

          <Form.Item label="Recipient Name" name="recipientName" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item label="Phone Number" name="phone" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item label="Tag (e.g., Home, Work)" name="tag">
            <Input />
          </Form.Item>

          <Form.Item>
            <div className="form-actions">
              <Button className="secondary-button" onClick={() => navigate(getReturnPath())}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" loading={addAddressLoading} className="primary-button">
                Save Address
              </Button>
            </div>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AddressForm;
