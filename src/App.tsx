import React from 'react';
import './App.css';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Menu from './components/Menu';
import Cart from './components/Cart';
import AddressSelection from './pages/AddressSelection';
import AddressForm from './pages/AddressForm';
import OrderSuccess from './pages/OrderSuccess';
import ErrorPage from './pages/ErrorPage';
import { CartProvider } from './context/CartContext';
import { RestaurantProvider } from './context/RestaurantContext';

const App = () => {
  return (
    <RestaurantProvider>
      <CartProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Restaurant menu routes */}
              <Route
                path="/:restaurantId/:token"
                element={
                  <>
                    <Menu />
                    <Cart />
                  </>
                }
              />

              {/* Restaurant address routes */}
              <Route path="/:restaurantId/a/:token" element={<AddressSelection />} />
              <Route path="/:restaurantId/a/:token/add" element={<AddressForm />} />
              <Route path="/:restaurantId/a/:token/edit/:addressId" element={<AddressForm />} />

              {/* Global routes */}
              <Route path="/success" element={<OrderSuccess />} />
              <Route path="/error" element={<ErrorPage />} />

              {/* Home page - empty for now */}
              <Route path="/" element={<div></div>} />
            </Routes>
          </div>
        </Router>
      </CartProvider>
    </RestaurantProvider>
  );
};

export default App;
