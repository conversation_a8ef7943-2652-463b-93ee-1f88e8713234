import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { IRestaurant } from '../types/restaurant';
import { loadRestaurantData, RestaurantDataResponse } from '../services/restaurantService';

interface RestaurantContextType {
  currentRestaurantId: string | null;
  restaurantData: IRestaurant | null;
  isLoading: boolean;
  error: string | null;
  loadRestaurant: (restaurantId: string) => Promise<void>;
  clearError: () => void;
}

const RestaurantContext = createContext<RestaurantContextType | undefined>(undefined);

interface RestaurantProviderProps {
  children: ReactNode;
}

export const RestaurantProvider: React.FC<RestaurantProviderProps> = ({ children }) => {
  const [currentRestaurantId, setCurrentRestaurantId] = useState<string | null>(null);
  const [restaurantData, setRestaurantData] = useState<IRestaurant | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = () => {
    setError(null);
  };

  const loadRestaurant = async (restaurantId: string) => {
    // Don't reload if it's the same restaurant
    if (currentRestaurantId === restaurantId && restaurantData) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(`Loading restaurant data for: ${restaurantId}`);
      const data = await loadRestaurantData(restaurantId);

      // Extract the restaurant object from the response
      setRestaurantData(data.restaurant);
      setCurrentRestaurantId(restaurantId);

      console.log(`Successfully loaded restaurant: ${data.restaurant.name}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load restaurant data';
      setError(errorMessage);
      console.error('Error loading restaurant:', err);

      // Clear restaurant data on error
      setRestaurantData(null);
      setCurrentRestaurantId(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Clear restaurant data when restaurant ID changes
  useEffect(() => {
    if (currentRestaurantId && !restaurantData) {
      loadRestaurant(currentRestaurantId);
    }
  }, [currentRestaurantId]);

  const contextValue: RestaurantContextType = {
    currentRestaurantId,
    restaurantData,
    isLoading,
    error,
    loadRestaurant,
    clearError,
  };

  return <RestaurantContext.Provider value={contextValue}>{children}</RestaurantContext.Provider>;
};

export const useRestaurant = (): RestaurantContextType => {
  const context = useContext(RestaurantContext);
  if (context === undefined) {
    throw new Error('useRestaurant must be used within a RestaurantProvider');
  }
  return context;
};
